<!-- src/lib/components/features/error-handling/ErrorBoundary.svelte -->
<script lang="ts">
  import { onDestroy, onMount } from 'svelte';

  import { createModuleLogger } from '$lib/utils/logger';

  import { ErrorDisplay } from './index';
  import { ErrorRecoveryHelper } from './index';
  import type { ErrorContext, ErrorBoundaryLevel } from './types';

  // Props - 使用新的简化API
  export let level: ErrorBoundaryLevel = 'component';
  export let fallback: any = null;
  export let showDetails = false;
  export let enableRecovery = true;
  export let onError: ((error: Error, context?: ErrorContext) => void) | null = null;

  // 错误状态
  let hasError = false;
  let error: Error | null = null;
  let errorContext: ErrorContext | null = null;

  const logger = createModuleLogger(`error-boundary-${level}`);

  /**
   * 重置错误状态
   */
  function resetError() {
    hasError = false;
    error = null;
    errorContext = null;
    logger.info('ErrorBoundary reset');
  }

  /**
   * 处理错误边界触发事件
   */
  function handleErrorBoundaryTrigger(event: Event) {
    const customEvent = event as CustomEvent;
    const { error: triggerError, context: triggerContext } = customEvent.detail;

    // 只处理与当前边界级别匹配的错误
    if (!shouldHandleError(triggerError, triggerContext)) {
      return;
    }

    hasError = true;
    error = triggerError;
    errorContext = triggerContext;

    logger.error('ErrorBoundary triggered', {
      level,
      errorMessage: triggerError.message,
      errorType: triggerContext?.type,
      severity: triggerContext?.severity,
    });

    // 调用外部错误处理器
    if (onError) {
      try {
        onError(triggerError, triggerContext);
      } catch (handlerError) {
        logger.error('Error in onError handler', { handlerError });
      }
    }
  }

  /**
   * 判断是否应该处理该错误
   */
  function shouldHandleError(err: Error, context?: ErrorContext): boolean {
    if (!context) return level === 'page'; // 无上下文的错误由页面级边界处理

    // 根据严重程度和边界级别决定
    switch (level) {
      case 'page':
        // 页面级边界处理所有critical和high严重程度的错误
        return context.severity === 'critical' || context.severity === 'high';
      
      case 'section':
        // 区域级边界处理medium和high严重程度的组件错误
        return context.type === 'component' && 
               (context.severity === 'medium' || context.severity === 'high');
      
      case 'component':
        // 组件级边界主要处理low和medium严重程度的错误
        return context.severity === 'low' || context.severity === 'medium';
      
      default:
        return false;
    }
  }

  /**
   * 获取错误边界标题
   */
  function getErrorTitle(): string {
    switch (level) {
      case 'page':
        return '页面遇到了问题';
      case 'section':
        return '这个区域出现了问题';
      case 'component':
        return '组件加载失败';
      default:
        return '出现错误';
    }
  }

  onMount(() => {
    // 监听 errorManager 触发的错误边界事件
    window.addEventListener('error-boundary-trigger', handleErrorBoundaryTrigger as EventListener);

    logger.debug(`ErrorBoundary (${level}) mounted and listening for error boundary triggers`);
  });

  onDestroy(() => {
    // 清理事件监听器
    window.removeEventListener('error-boundary-trigger', handleErrorBoundaryTrigger as EventListener);

    logger.debug(`ErrorBoundary (${level}) destroyed and event listeners removed`);
  });

  // 导出处理函数供外部调用
  export { resetError };
</script>

{#if hasError && error}
  {#if fallback}
    <!-- 使用自定义的错误组件 -->
    <svelte:component this={fallback} {error} context={errorContext} {resetError} />
  {:else}
    <!-- 默认错误页面 -->
    <div class="flex min-h-[200px] items-center justify-center p-4">
      <div class="w-full max-w-2xl space-y-4">
        <ErrorDisplay
          {error}
          title={getErrorTitle()}
          showRetry={enableRecovery}
          showDetails={showDetails}
          onRetry={resetError}
          onCopy={() => {
            // 复制成功的通知应该由 ErrorDisplay 内部处理
          }}
        />

        {#if enableRecovery && errorContext?.severity !== 'critical'}
          <ErrorRecoveryHelper
            {error}
            context={errorContext || undefined}
            onRecovered={resetError}
            onFailed={() => {
              // 恢复失败时的处理
              logger.warn('Error recovery failed');
            }}
          />
        {/if}
      </div>
    </div>
  {/if}
{:else}
  <!-- 正常渲染子组件 -->
  <slot />
{/if}