/**
 * 错误处理相关的类型定义
 *
 * @category Types
 */

/**
 * 错误严重程度
 */
export type ErrorSeverity = 'low' | 'medium' | 'high' | 'critical';

/**
 * 错误类型
 */
export type ErrorType = 'network' | 'component' | 'route' | 'api' | 'script' | 'unknown';

/**
 * 错误边界级别
 */
export type ErrorBoundaryLevel = 'page' | 'component' | 'section';

/**
 * 精简的错误上下文信息
 */
export interface ErrorContext {
  /** 错误类型 */
  type: ErrorType;
  /** 错误严重程度 */
  severity: ErrorSeverity;
  /** 组件名称 */
  component?: string;
  /** 操作名称 */
  operation?: string;
  /** 路由信息 */
  route?: string;
  /** 是否抑制通知显示 */
  suppressNotification?: boolean;
  /** 发生时间戳 */
  timestamp: string;
  /** 附加元数据 */
  metadata?: Record<string, any>;
}

/**
 * 错误处理器函数类型
 */
export type ErrorHandler = (error: Error, context?: ErrorContext) => void;

/**
 * 错误边界组件属性
 */
export interface ErrorBoundaryProps {
  /** 错误边界级别 */
  level?: ErrorBoundaryLevel;
  /** 自定义错误回退组件 */
  fallback?: any;
  /** 是否显示错误详情 */
  showDetails?: boolean;
  /** 是否启用错误恢复功能 */
  enableRecovery?: boolean;
  /** 错误处理回调函数 */
  onError?: ErrorHandler;
}

/**
 * 错误恢复组件属性
 */
export interface ErrorRecoveryProps {
  /** 错误对象 */
  error: Error;
  /** 错误上下文 */
  context?: ErrorContext;
  /** 重置错误状态的函数 */
  resetError: () => void;
}

/**
 * 错误统计信息
 */
export interface ErrorStats {
  /** 错误ID */
  errorId: string;
  /** 错误指纹 */
  fingerprint: string;
  /** 错误发生时间 */
  timestamp: string;
  /** 错误上下文 */
  context: ErrorContext;
  /** 错误消息 */
  message: string;
  /** 用户代理 */
  userAgent: string;
  /** 页面URL */
  url: string;
  /** 是否已解决 */
  resolved: boolean;
  /** 发生次数 */
  count: number;
}

/**
 * 错误管理器配置
 */
export interface ErrorManagerConfig {
  /** 是否启用全局错误捕获 */
  enableGlobalCapture: boolean;
  /** 是否启用错误上报 */
  enableReporting: boolean;
  /** 错误上报端点 */
  reportingEndpoint?: string;
  /** 最大错误缓存数量 */
  maxErrorCache: number;
  /** 是否在开发环境显示详细错误 */
  showDetailsInDev: boolean;
  /** 错误去重时间窗口（毫秒） */
  deduplicationWindow: number;
  /** 采样率 (0-1) */
  sampleRate: number;
}

/**
 * 错误上报数据
 */
export interface ErrorReport {
  /** 错误ID */
  errorId: string;
  /** 错误指纹（用于去重） */
  fingerprint: string;
  /** 错误消息 */
  message: string;
  /** 错误堆栈 */
  stack?: string;
  /** 错误上下文 */
  context: ErrorContext;
  /** 发生时间 */
  timestamp: string;
  /** 用户代理 */
  userAgent: string;
  /** 页面URL */
  url: string;
  /** 用户ID（如果有） */
  userId?: string;
  /** 会话ID（如果有） */
  sessionId?: string;
  /** 重复次数 */
  count?: number;
}

/**
 * 错误指纹生成器
 */
export interface ErrorFingerprint {
  /** 生成错误指纹 */
  generate(error: Error, context: ErrorContext): string;
}

/**
 * 错误严重程度计算器
 */
export interface ErrorSeverityCalculator {
  /** 计算错误严重程度 */
  calculate(error: Error, context: ErrorContext): ErrorSeverity;
}

// 为了向后兼容，保留旧的接口（标记为已弃用）
/**
 * @deprecated 请使用 ErrorContext 替代
 */
export type ErrorInfo = ErrorContext;

/**
 * @deprecated 请使用新的错误处理机制
 */
export interface RouteErrorHandler {
  handleRouteError(error: Error, route?: string): void;
  isRouteError(error: Error): boolean;
}