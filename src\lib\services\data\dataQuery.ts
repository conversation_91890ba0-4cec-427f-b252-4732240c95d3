// 数据查询服务 - 整合原有的查询功能
import type {
  BaseQueryParams,
  MarketEvent
} from '$lib/types';
import type {
  AdvancedQueryParams,
  AdvancedFilterObj,
  FilterItem
} from '$lib/types/business/market';

import { BaseApiService } from '../api/base';

// 基础查询参数类型（原有的查询方式）
interface BasicQueryParams {
  base: string[]; // 币种名称列表
  date: string; // 查询日期 (UTC)，格式：yyyy-MM-dd
  marketEventType: string[]; // 市场事件类型列表
  threshold?: number; // 阈值，枚举值：0, 1, 2, 4
}

interface QueryResult {
  items: MarketEvent[];
  total: number;
}

// API 响应类型格式（符合API文档规范）
interface DataQueryResponse {
  code: number;
  message: string;
  data: {
    date: string;
    events: MarketEvent[];
  };
}

/**
 * 数据查询服务类
 *
 * 提供灵活的数据查询、筛选、分页和导出功能。
 * 支持多种查询条件组合，包括关键词搜索、状态筛选、日期范围等。
 * 使用 MSW (Mock Service Worker) 进行开发环境的数据模拟。
 * 继承 BaseApiService 获得统一的错误处理、重试机制和超时控制。
 *
 * @example
 * ```typescript
 * import { dataQueryService } from '$lib/services/data/dataQuery';
 *
 * // 执行查询
 * const result = await dataQueryService.queryData({
 *   keyword: '搜索关键词',
 *   status: '有效',
 *   startDate: '2024-01-01',
 *   endDate: '2024-01-31',
 *   page: 1,
 *   pageSize: 10
 * });
 *
 * apiLogger.info(`查询完成，找到 ${result.total} 条记录`, { total: result.total });
 * apiLogger.debug('查询结果数据', { items: result.items });
 *
 * // 导出数据
 * const blob = await dataQueryService.exportData(queryParams);
 * // 创建下载链接...
 * ```
 *
 * @category Services
 */
export class DataQueryService extends BaseApiService {

  /**
   * 查询事件数据
   *
   * 根据API规范查询指定日期的市场事件数据。
   * 使用 BaseApiService 的 GET 方法，自动获得重试、超时和错误处理功能。
   *
   * @param params - 查询参数对象
   * @param params.base - 币种名称列表，例如 ['BTC', 'ETH']
   * @param params.date - 查询日期 (UTC)，格式：yyyy-MM-dd
   * @param params.marketEventType - 市场事件类型列表
   * @param params.threshold - 阈值，枚举值：0, 1, 2, 4（可选）
   * @returns Promise 包含查询结果和总数的对象
   *
   * @example
   * ```typescript
   * const result = await dataQueryService.queryData({
   *   base: ['BTC', 'ETH'],
   *   date: '2025-04-24',
   *   marketEventType: ['LIQUIDATION_ORDER', 'FUNDING_RATE_SWITCH'],
   *   threshold: 0
   * });
   *
   * console.log(`找到 ${result.total} 条事件记录`);
   * result.items.forEach(item => {
   *   console.log(`${item.marketEventType} - ${item.date}`);
   * });
   * ```
   */
  async queryData(params: BasicQueryParams): Promise<QueryResult> {
    // 构建查询参数
    const searchParams = new URLSearchParams();

    // 必需参数
    if (params.base && params.base.length > 0) {
      searchParams.append('base', params.base.join(','));
    }
    if (params.date) {
      searchParams.append('date', params.date);
    }
    if (params.marketEventType && params.marketEventType.length > 0) {
      searchParams.append('marketEventType', params.marketEventType.join(','));
    }

    // 可选参数
    if (params.threshold !== undefined) {
      searchParams.append('threshold', params.threshold.toString());
    }

    const response = await this.get<DataQueryResponse>(
      `/event/query?${searchParams.toString()}`,
      {
        headers: {
          'Accept': 'application/json',
        },
      }
    );

    const apiResponse = response.data;

    // 检查API响应状态
    if (apiResponse.code !== 1000 || apiResponse.message !== 'success') {
      throw new Error(`API Error: ${apiResponse.message || '未知错误'}`);
    }

    // 返回适配后的数据格式
    return {
      items: apiResponse.data.events,
      total: apiResponse.data.events.length,
    };
  }

  /**
   * 高级查询事件数据
   *
   * 根据API规范使用 /event/query/advance 接口查询指定日期的市场事件数据。
   * 支持多个过滤条件组合，每个过滤条件可以设置不同的事件类型和阈值。
   * 使用 BaseApiService 的 GET 方法，自动获得重试、超时和错误处理功能。
   *
   * @param params - 高级查询参数对象
   * @param params.date - 查询日期 (UTC)，格式：yyyy-MM-dd
   * @param params.base - 币种名称列表，例如 ['BTC', 'ETH']
   * @param params.filters - 过滤条件数组，每个条件包含事件类型和阈值
   * @returns Promise 包含查询结果和总数的对象
   *
   * @example
   * ```typescript
   * const result = await dataQueryService.queryAdvancedData({
   *   date: '2025-04-24',
   *   base: ['BTC', 'ETH'],
   *   filters: [
   *     { id: '1', marketEventType: 'TWAP', threshold: 4 },
   *     { id: '2', marketEventType: 'LIQUIDATION_ORDER', threshold: 2 }
   *   ]
   * });
   *
   * console.log(`找到 ${result.total} 条事件记录`);
   * ```
   */
  async queryAdvancedData(params: AdvancedQueryParams): Promise<QueryResult> {
    // 构建 filterObj 对象
    const filterObj: AdvancedFilterObj = {
      date: params.date,
      base: params.base.join(','), // 转换为逗号分隔的字符串
      filters: params.filters.map((filter: FilterItem) => ({
        marketEventType: filter.marketEventType, // 直接使用 TWAP 格式
        threshold: filter.threshold
      }))
    };

    // URL 编码 filterObj
    const encodedFilterObj = encodeURIComponent(JSON.stringify(filterObj));

    const response = await this.get<DataQueryResponse>(
      `/event/query/advance?filterObj=${encodedFilterObj}`,
      {
        headers: {
          'Accept': 'application/json',
        },
      }
    );

    const apiResponse = response.data;

    // 检查API响应状态
    if (apiResponse.code !== 1000 || apiResponse.message !== 'success') {
      throw new Error(`API Error: ${apiResponse.message || '未知错误'}`);
    }

    // 返回适配后的数据格式
    return {
      items: apiResponse.data.events,
      total: apiResponse.data.events.length,
    };
  }

  /**
   * 导出事件数据
   * 使用 BaseApiService 的底层请求方法，直接返回 Blob 类型。
   */
  async exportData(params: BasicQueryParams): Promise<Blob> {
    // 构建查询参数
    const searchParams = new URLSearchParams();

    // 必需参数
    if (params.base && params.base.length > 0) {
      searchParams.append('base', params.base.join(','));
    }
    if (params.date) {
      searchParams.append('date', params.date);
    }
    if (params.marketEventType && params.marketEventType.length > 0) {
      searchParams.append('marketEventType', params.marketEventType.join(','));
    }

    // 可选参数
    if (params.threshold !== undefined) {
      searchParams.append('threshold', params.threshold.toString());
    }

    // 直接使用 BaseApiService 的保护方法进行底层请求
    const response = await this.request<ArrayBuffer>(
      `/event/export?${searchParams.toString()}`,
      {
        method: 'GET',
        headers: {
          'Accept': 'application/octet-stream',
        },
      }
    );

    // 转换为 Blob
    return new Blob([response.data], { type: 'application/octet-stream' });
  }
}

// 创建默认实例
export const dataQueryService = new DataQueryService('');
