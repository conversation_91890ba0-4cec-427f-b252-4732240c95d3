// 爆仓数据服务 - 整合原有的爆仓数据功能
import type {
  LiquidationData,
  LiquidationRankResponse,
  LiquidationStats,
  RankQueryParams,
  SnapshotTimeRange,
  TrendDuration,
  TrendTimeFrame,
  TrendTimeRange,
} from '$lib/types/business';
import type { ApiResponse } from '$lib/types/core';

import { BaseApiService } from '../api/base';

/**
 * 爆仓数据服务类
 *
 * 专门处理加密货币市场的爆仓数据，提供快照数据、趋势分析和实时监控功能。
 * 支持多种时间粒度和视图模式，包括全市场视图和主流币/山寨币对比视图。
 * 继承 BaseApiService 获得统一的错误处理、重试机制和超时控制。
 *
 * @example
 * ```typescript
 * import { liquidationDataService } from '$lib/services/data/liquidation';
 *
 * // 获取24小时快照数据
 * const snapshotResponse = await liquidationDataService.fetchSnapshotData('24h');
 * if (snapshotResponse.status === 'success') {
 *   const { data, stats } = snapshotResponse.data;
 *   console.log(`总爆仓金额: $${stats.totalAmount.toLocaleString()}`);
 *   console.log(`多空比: ${stats.longShortRatio.toFixed(2)}`);
 * }
 *
 * // 获取7天趋势数据
 * const trendResponse = await liquidationDataService.fetchTrendData('7d');
 *
 * // 开始自动刷新（每分钟）
 * liquidationDataService.startAutoRefresh(60000);
 *
 * // 停止自动刷新
 * liquidationDataService.stopAutoRefresh();
 * ```
 *
 * @category Services
 */
export class LiquidationDataService extends BaseApiService {
  private refreshInterval: number | null = null;

  /**
   * 获取快照数据
   * 使用 BaseApiService 的 POST 方法，自动获得重试、超时和错误处理功能。
   */
  async fetchSnapshotData(timeRange: SnapshotTimeRange): Promise<
    ApiResponse<{
      data: LiquidationData[];
      stats: LiquidationStats;
    }>
  > {
    return this.post<{
      data: LiquidationData[];
      stats: LiquidationStats;
    }>('/liquidation/snapshot', { timeRange });
  }

  /**
   * 获取趋势数据
   * 使用 BaseApiService 的 POST 方法，自动获得重试、超时和错误处理功能。
   */
  async fetchTrendData(timeRange: TrendTimeRange): Promise<ApiResponse<LiquidationData[]>> {
    return this.post<LiquidationData[]>('/liquidation/trend', { timeRange });
  }

  /**
   * 获取带时间粒度的趋势数据
   * 使用 BaseApiService 的 POST 方法，自动获得重试、超时和错误处理功能。
   */
  async fetchTrendDataWithFrameAndDuration(
    timeFrame: TrendTimeFrame,
    duration: TrendDuration
  ): Promise<ApiResponse<LiquidationData[]>> {
    return this.post<LiquidationData[]>('/liquidation/trend-detailed', {
      timeFrame,
      duration,
    });
  }

  /**
   * 开始自动刷新
   */
  startAutoRefresh(interval: number = 60000): void {
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval);
    }

    this.refreshInterval = window.setInterval(() => {
      // 这里可以触发数据刷新的回调
      // 实际项目中可以在这里触发数据刷新事件
    }, interval);
  }

  /**
   * 停止自动刷新
   */
  stopAutoRefresh(): void {
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval);
      this.refreshInterval = null;
    }
  }

  /**
   * 获取清算排行榜数据
   * 使用 BaseApiService 的 POST 方法，自动获得重试、超时和统一错误处理功能。
   *
   * @param params 查询参数，包括排序、筛选、分页等
   * @returns 排行榜数据响应
   *
   * @example
   * ```typescript
   * // 获取按清算金额排序的前30名
   * const rankResponse = await liquidationDataService.fetchRankData({
   *   sortField: 'totalAmount',
   *   sortDirection: 'desc',
   *   coinTypeFilter: 'all',
   *   timeRange: '24h'
   * });
   *
   * if (rankResponse.status === 'success') {
   *   const { items, summary } = rankResponse.data;
   *   console.log(`显示前 ${items.length} 个币种的排行榜`);
   *   items.forEach(item => {
   *     console.log(`${item.rank}. ${item.symbol}: $${item.totalAmount.toLocaleString()}`);
   *   });
   * }
   * ```
   */
  async fetchRankData(params: RankQueryParams): Promise<ApiResponse<LiquidationRankResponse>> {
    return this.post<LiquidationRankResponse>('/liquidation/rank', params);
  }
}

// 创建默认实例
export const liquidationDataService = new LiquidationDataService();
