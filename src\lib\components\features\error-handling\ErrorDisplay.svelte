<!-- src/lib/components/features/error-handling/ErrorDisplay.svelte -->
<script lang="ts">
  import CopyIcon from '@lucide/svelte/icons/copy';
  import RefreshCwIcon from '@lucide/svelte/icons/refresh-cw';

  import { Alert, AlertDescription } from '$lib/components/ui/alert';
  import { Button } from '$lib/components/ui/button';
  import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
  } from '$lib/components/ui/card';
  import { Separator } from '$lib/components/ui/separator';
  import { ApiError } from '$lib/services/api/base';

  // Props
  export let error: Error | ApiError | string;
  export let title = '出现错误';
  export let showRetry = true;
  export let showDetails = false;
  export let showCopy = true;
  export let variant: 'card' | 'alert' | 'inline' = 'card';
  export let size: 'sm' | 'md' | 'lg' = 'md';

  // Events - Svelte 5 style
  export let onRetry: () => void = () => {};
  export let onCopy: (text: string) => void = () => {};

  // 处理错误对象
  $: errorObj = typeof error === 'string' ? new Error(error) : error;
  $: isApiError = errorObj instanceof ApiError;

  // 获取用户友好的错误信息
  $: userMessage = getUserFriendlyMessage(errorObj);
  $: technicalMessage = getTechnicalMessage(errorObj);
  $: suggestions = getRecoverySuggestions(errorObj);
  $: errorIcon = getErrorIcon(errorObj);

  function getUserFriendlyMessage(err: Error | ApiError): string {
    if (err instanceof ApiError) {
      return err.getUserFriendlyMessage();
    }

    // 常见错误类型的友好提示
    if (err.name === 'TypeError' && err.message.includes('fetch')) {
      return '网络连接失败，请检查您的网络连接';
    }

    if (err.name === 'SyntaxError') {
      return '数据格式错误，请稍后重试';
    }

    if (err.message.includes('timeout')) {
      return '请求超时，请检查网络连接后重试';
    }

    if (err.message.includes('404')) {
      return '请求的资源不存在';
    }

    if (err.message.includes('500')) {
      return '服务器内部错误，请稍后重试';
    }

    // 默认友好提示
    return '系统遇到了一个问题，我们正在努力解决';
  }

  function getTechnicalMessage(err: Error | ApiError): string {
    if (err instanceof ApiError) {
      return `${err.name}: ${err.message} (状态码: ${err.statusCode})`;
    }
    return `${err.name}: ${err.message}`;
  }

  function getRecoverySuggestions(err: Error | ApiError): string[] {
    const suggestions: string[] = [];

    if (err instanceof ApiError) {
      if (err.statusCode && err.statusCode >= 500) {
        suggestions.push('服务器暂时不可用，请稍后重试');
        suggestions.push('如果问题持续存在，请联系技术支持');
      } else if (err.statusCode === 404) {
        suggestions.push('请检查请求的地址是否正确');
        suggestions.push('尝试返回首页重新开始');
      } else if (err.statusCode === 401 || err.statusCode === 403) {
        suggestions.push('请检查您的登录状态');
        suggestions.push('尝试重新登录');
      }
    } else {
      if (err.name === 'TypeError' && err.message.includes('fetch')) {
        suggestions.push('检查网络连接是否正常');
        suggestions.push('尝试刷新页面');
      } else if (err.message.includes('timeout')) {
        suggestions.push('检查网络连接速度');
        suggestions.push('尝试重新加载');
      } else {
        suggestions.push('尝试刷新页面');
        suggestions.push('清除浏览器缓存');
      }
    }

    return suggestions;
  }

  function getErrorIcon(err: Error | ApiError): string {
    if (err instanceof ApiError) {
      if (err.statusCode && err.statusCode >= 500) return '🔧';
      if (err.statusCode === 404) return '🔍';
      if (err.statusCode === 401 || err.statusCode === 403) return '🔒';
    }

    if (err.name === 'TypeError') return '🔗';
    if (err.name === 'SyntaxError') return '📝';
    if (err.message.includes('timeout')) return '⏱️';

    return '⚠️';
  }

  function handleCopy() {
    const errorText = `错误信息: ${technicalMessage}\n堆栈: ${errorObj.stack || '无'}`;
    navigator.clipboard?.writeText(errorText);
    onCopy(errorText);
  }

  // 样式计算
  $: sizeClass = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg'
  }[size];

  $: containerClass = variant === 'inline' ? 'border-0 shadow-none p-0' : '';
</script>

{#if variant === 'alert'}
  <Alert class="border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive">
    <AlertDescription class="flex items-center justify-between">
      <div class="flex items-center gap-2">
        <span class="text-lg">{errorIcon}</span>
        <span>{userMessage}</span>
      </div>
      {#if showRetry}
        <Button variant="outline" size="sm" onclick={() => onRetry()}>
          <RefreshCwIcon class="h-4 w-4" />
          重试
        </Button>
      {/if}
    </AlertDescription>
  </Alert>
{:else}
  <Card class={containerClass}>
    <CardHeader class="text-center">
      <div class="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-destructive/10">
        <span class="text-2xl">{errorIcon}</span>
      </div>
      <CardTitle class={sizeClass}>{title}</CardTitle>
      <CardDescription class="text-muted-foreground">
        {userMessage}
      </CardDescription>
    </CardHeader>

    <CardContent class="space-y-4">
      {#if suggestions.length > 0}
        <div class="space-y-2">
          <h4 class="text-sm font-medium">建议的解决方案：</h4>
          <ul class="space-y-1 text-sm text-muted-foreground">
            {#each suggestions as suggestion}
              <li class="flex items-start gap-2">
                <span class="mt-1.5 h-1 w-1 rounded-full bg-muted-foreground/50"></span>
                {suggestion}
              </li>
            {/each}
          </ul>
        </div>
      {/if}

      {#if showDetails}
        <div class="space-y-2">
          <Separator />
          <details class="group">
            <summary class="cursor-pointer text-sm font-medium text-muted-foreground hover:text-foreground">
              技术详情
            </summary>
            <div class="mt-2 space-y-2 text-xs">
              <div class="rounded bg-muted p-2 font-mono">
                {technicalMessage}
              </div>
              {#if errorObj.stack}
                <div class="rounded bg-muted p-2 font-mono text-xs">
                  <div class="mb-1 font-medium">堆栈跟踪：</div>
                  <pre class="whitespace-pre-wrap">{errorObj.stack}</pre>
                </div>
              {/if}
            </div>
          </details>
        </div>
      {/if}

      <div class="flex gap-2">
        {#if showRetry}
          <Button onclick={() => onRetry()} class="flex-1">
            <RefreshCwIcon class="mr-2 h-4 w-4" />
            重试
          </Button>
        {/if}
        {#if showCopy}
          <Button variant="outline" onclick={() => handleCopy()}>
            <CopyIcon class="mr-2 h-4 w-4" />
            复制错误信息
          </Button>
        {/if}
      </div>
    </CardContent>
  </Card>
{/if}
