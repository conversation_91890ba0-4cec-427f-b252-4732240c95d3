/**
 * 错误处理模块统一导出
 *
 * @category Components
 */

export { default as ErrorBoundary } from './ErrorBoundary.svelte';
export { default as ErrorDisplay } from './ErrorDisplay.svelte';
export { default as ErrorRecoveryHelper } from './ErrorRecoveryHelper.svelte';
export type {
  ErrorBoundaryProps,
  ErrorHandler,
  ErrorInfo,
  ErrorManagerConfig,
  ErrorRecoveryProps,
  ErrorReport,
  ErrorStats,
  RouteErrorHandler,
} from './types';
