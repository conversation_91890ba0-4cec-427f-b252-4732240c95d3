// src/lib/services/api/base.ts
import type { ApiResponse } from '$lib/types';
import { logApiError } from '$lib/utils/logger';

/**
 * 重试配置接口
 */
export interface RetryConfig {
  /** 最大重试次数 */
  maxRetries: number;
  /** 重试延迟（毫秒） */
  retryDelay: number;
  /** 是否使用指数退避 */
  exponentialBackoff: boolean;
  /** 可重试的状态码 */
  retryableStatusCodes: number[];
}

/**
 * 请求配置接口
 */
export interface RequestConfig extends RequestInit {
  /** 请求超时时间（毫秒） */
  timeout?: number;
  /** 重试配置 */
  retry?: Partial<RetryConfig>;
  /** 是否跳过错误日志 */
  skipErrorLogging?: boolean;
}

/**
 * 基础 API 服务类
 * 提供统一的请求处理、错误处理和响应格式化
 */
export class BaseApiService {
  protected baseURL: string;
  protected defaultHeaders: Record<string, string>;
  protected defaultRetryConfig: RetryConfig;

  constructor(baseURLParam?: string) {
    // 如果 baseURLParam 被显式提供（即使是空字符串），则使用它
    if (baseURLParam !== undefined) {
      this.baseURL = baseURLParam;
    } else {
      // 否则，回退到环境变量或默认的 '/api'
      this.baseURL = import.meta.env.VITE_API_BASE_URL || '/api';
    }
    this.defaultHeaders = {
      'Content-Type': 'application/json',
    };
    this.defaultRetryConfig = {
      maxRetries: 3,
      retryDelay: 1000,
      exponentialBackoff: true,
      retryableStatusCodes: [408, 429, 500, 502, 503, 504],
    };
  }

  /**
   * 统一的请求方法（带重试和超时）
   */
  protected async request<T>(
    endpoint: string,
    options: RequestConfig = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`;
    const retryConfig = { ...this.defaultRetryConfig, ...options.retry };
    const timeout = options.timeout || 10000; // 默认10秒超时

    let lastError: Error | null = null;

    for (let attempt = 0; attempt <= retryConfig.maxRetries; attempt++) {
      try {
        const response = await this.makeRequest<T>(url, options, timeout);
        return response;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));

        // 检查是否应该重试
        if (attempt < retryConfig.maxRetries && this.shouldRetry(lastError, retryConfig)) {
          const delay = this.calculateRetryDelay(attempt, retryConfig);
          await this.sleep(delay);
          continue;
        }

        // 不重试或已达到最大重试次数
        break;
      }
    }

    // 记录错误日志
    if (!options.skipErrorLogging && lastError) {
      const statusCode = this.extractStatusCode(lastError);
      logApiError(lastError, endpoint, options.method || 'GET', statusCode, {
        requestId: Math.random().toString(36).substr(2, 9),
        url,
        attempts: retryConfig.maxRetries + 1,
      });
    }

    throw new ApiError(lastError?.message || 'Request failed', endpoint);
  }

  /**
   * 执行单次请求
   */
  private async makeRequest<T>(
    url: string,
    options: RequestConfig,
    timeout: number
  ): Promise<ApiResponse<T>> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      const response = await fetch(url, {
        ...options,
        headers: {
          ...this.defaultHeaders,
          ...options.headers,
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new ApiError(`HTTP ${response.status}: ${response.statusText}`, url, response.status);
      }

      const data = await response.json();

      return {
        data,
        status: 'success',
      };
    } catch (error) {
      clearTimeout(timeoutId);

      if (error instanceof Error && error.name === 'AbortError') {
        throw new ApiError('Request timeout', url, 408);
      }

      throw error;
    }
  }

  /**
   * 判断是否应该重试
   */
  private shouldRetry(error: Error, retryConfig: RetryConfig): boolean {
    if (error instanceof ApiError) {
      return retryConfig.retryableStatusCodes.includes(error.statusCode || 0);
    }

    // 网络错误通常可以重试
    return error.name === 'TypeError' || error.message.includes('fetch');
  }

  /**
   * 计算重试延迟
   */
  private calculateRetryDelay(attempt: number, retryConfig: RetryConfig): number {
    if (retryConfig.exponentialBackoff) {
      return retryConfig.retryDelay * Math.pow(2, attempt);
    }
    return retryConfig.retryDelay;
  }

  /**
   * 延迟函数
   */
  private sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * 从错误中提取状态码
   */
  private extractStatusCode(error: Error): number | undefined {
    if (error instanceof ApiError) {
      return error.statusCode;
    }

    const match = error.message.match(/HTTP (\d+):/);
    return match ? parseInt(match[1], 10) : undefined;
  }

  /**
   * GET 请求
   */
  protected async get<T>(
    endpoint: string,
    config?: Partial<RequestConfig>
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...config, method: 'GET' });
  }

  /**
   * POST 请求
   */
  protected async post<T>(
    endpoint: string,
    data?: unknown,
    config?: Partial<RequestConfig>
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      ...config,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * PUT 请求
   */
  protected async put<T>(
    endpoint: string,
    data?: unknown,
    config?: Partial<RequestConfig>
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      ...config,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * DELETE 请求
   */
  protected async delete<T>(
    endpoint: string,
    config?: Partial<RequestConfig>
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...config, method: 'DELETE' });
  }
}

/**
 * API 错误类
 */
export class ApiError extends Error {
  public endpoint: string;
  public statusCode?: number;
  public response?: Response;

  constructor(message: string, endpoint: string, statusCode?: number, response?: Response) {
    super(message);
    this.name = 'ApiError';
    this.endpoint = endpoint;
    this.statusCode = statusCode;
    this.response = response;
  }

  /**
   * 判断是否为网络错误
   */
  isNetworkError(): boolean {
    return !this.statusCode || this.statusCode === 0;
  }

  /**
   * 判断是否为客户端错误 (4xx)
   */
  isClientError(): boolean {
    return this.statusCode ? this.statusCode >= 400 && this.statusCode < 500 : false;
  }

  /**
   * 判断是否为服务器错误 (5xx)
   */
  isServerError(): boolean {
    return this.statusCode ? this.statusCode >= 500 : false;
  }

  /**
   * 判断是否为超时错误
   */
  isTimeoutError(): boolean {
    return this.statusCode === 408 || this.message.includes('timeout');
  }

  /**
   * 获取用户友好的错误消息
   */
  getUserFriendlyMessage(): string {
    if (this.isNetworkError()) {
      return '网络连接失败，请检查您的网络连接';
    }

    if (this.isTimeoutError()) {
      return '请求超时，请稍后重试';
    }

    if (this.statusCode === 401) {
      return '身份验证失败，请重新登录';
    }

    if (this.statusCode === 403) {
      return '您没有权限执行此操作';
    }

    if (this.statusCode === 404) {
      return '请求的资源不存在';
    }

    if (this.statusCode === 429) {
      return '请求过于频繁，请稍后重试';
    }

    if (this.isServerError()) {
      return '服务器暂时不可用，请稍后重试';
    }

    return this.message || '请求失败，请稍后重试';
  }
}

/**
 * 创建默认的 API 服务实例
 */
export const createApiService = (baseURL?: string) => new BaseApiService(baseURL);
