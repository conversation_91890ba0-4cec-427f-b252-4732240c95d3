<script lang="ts">
  import { SystemStatus } from '$lib/components/features';
  import { Button } from '$lib/components/ui/button';
  import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
  } from '$lib/components/ui/card';
  import { Progress } from '$lib/components/ui/progress';
  import { ApiError } from '$lib/services/api/base';
  import { networkStatus } from '$lib/utils/networkMonitor';
  import { Wrench } from '@lucide/svelte';
  import { goto, invalidateAll } from '$app/navigation';

  // Props
  export let error: Error | ApiError;
  export let context: import('./types').ErrorContext | undefined = undefined;
  export let autoRecovery = true;
  export let maxRetries = 3;
  export let retryDelay = 2000;

  // Svelte 5 slots declaration
  type $$Slots = { default: {}; };

  // Events - Svelte 5 style
  export let onRecovered: () => void = () => {};
  export let onFailed: () => void = () => {};
  export let onRetry: (attempt: number) => void = () => {};

  // 恢复状态
  let isRecovering = false;
  let currentAttempt = 0;
  let recoveryProgress = 0;
  let recoveryMessage = '';
  let recoveryTimer: NodeJS.Timeout | null = null;

  // 恢复策略
  $: recoveryStrategies = getRecoveryStrategies(error, context);

  interface RecoveryStrategy {
    name: string;
    description: string;
    action: () => Promise<boolean>;
    icon: string;
    priority: number;
  }

  function getRecoveryStrategies(err: Error | ApiError, ctx?: import('./types').ErrorContext): RecoveryStrategy[] {
    const strategies: RecoveryStrategy[] = [];

    if (!err) {
      console.warn("ErrorRecoveryHelper received an undefined or null error object.");
      return strategies;
    }

    // 根据错误上下文类型提供更精确的恢复策略
    if (ctx) {
      switch (ctx.type) {
        case 'network':
          strategies.push({
            name: '检查网络连接',
            description: '验证网络连接状态并重试请求',
            action: async () => {
              recoveryMessage = '正在检查网络连接...';
              await new Promise(resolve => setTimeout(resolve, 1000));
              return $networkStatus.isOnline;
            },
            icon: '🌐',
            priority: 1
          });
          break;
        
        case 'api':
          strategies.push({
            name: '重试API请求',
            description: '等待片刻后重新发送API请求',
            action: async () => {
              recoveryMessage = '正在重试API请求...';
              await new Promise(resolve => setTimeout(resolve, 2000));
              return Math.random() > 0.3; // 模拟70%成功率
            },
            icon: '🔄',
            priority: 1
          });
          break;
        
        case 'component':
          strategies.push({
            name: '重新加载组件',
            description: '尝试重新初始化出错的组件',
            action: async () => {
              recoveryMessage = '正在重新加载组件...';
              await new Promise(resolve => setTimeout(resolve, 1500));
              return true;
            },
            icon: '🔧',
            priority: 1
          });
          break;
      }
    }

    if (err instanceof ApiError) {
      if (err.isNetworkError()) {
        strategies.push({
          name: '检查网络连接',
          description: '验证网络连接状态并重试请求',
          action: async () => {
            recoveryMessage = '正在检查网络连接...';
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            if ($networkStatus.isOnline) {
              recoveryMessage = '网络连接正常，重试请求...';
              await new Promise(resolve => setTimeout(resolve, 1000));
              return true;
            }
            return false;
          },
          icon: '🌐',
          priority: 1
        });
      }

      if (err.isServerError()) {
        strategies.push({
          name: '等待服务恢复',
          description: '服务器可能暂时不可用，等待一段时间后重试',
          action: async () => {
            recoveryMessage = '等待服务器恢复...';
            await new Promise(resolve => setTimeout(resolve, 3000));
            recoveryMessage = '重试连接服务器...';
            await new Promise(resolve => setTimeout(resolve, 1000));
            return Math.random() > 0.3; // 模拟70%成功率
          },
          icon: '🔧',
          priority: 2
        });
      }

      if (err.isClientError() && err.statusCode !== 404) {
        strategies.push({
          name: '刷新页面数据',
          description: '重新加载页面数据以解决客户端错误',
          action: async () => {
            recoveryMessage = '正在刷新页面数据...';
            await invalidateAll();
            await new Promise(resolve => setTimeout(resolve, 1000));
            return true;
          },
          icon: '🔄',
          priority: 3
        });
      }
    } else {
      // JavaScript 错误的恢复策略
      if (err.name === 'TypeError') {
        strategies.push({
          name: '重新初始化组件',
          description: '重新加载组件以修复类型错误',
          action: async () => {
            recoveryMessage = '正在重新初始化组件...';
            await new Promise(resolve => setTimeout(resolve, 1500));
            return true;
          },
          icon: '🔄',
          priority: 1
        });
      }

      if (err.message.includes('chunk')) {
        strategies.push({
          name: '重新加载应用',
          description: '代码块加载失败，需要重新加载应用',
          action: async () => {
            recoveryMessage = '正在重新加载应用...';
            await new Promise(resolve => setTimeout(resolve, 1000));
            window.location.reload();
            return true;
          },
          icon: '🔄',
          priority: 1
        });
      }
    }

    // 通用恢复策略
    strategies.push({
      name: '返回首页',
      description: '导航到首页重新开始',
      action: async () => {
        recoveryMessage = '正在返回首页...';
        await goto('/');
        await new Promise(resolve => setTimeout(resolve, 500));
        return true;
      },
      icon: '🏠',
      priority: 10
    });

    return strategies.sort((a, b) => a.priority - b.priority);
  }

  async function startRecovery() {
    if (isRecovering || recoveryStrategies.length === 0) return;

    isRecovering = true;
    currentAttempt = 0;
    recoveryProgress = 0;

    for (const strategy of recoveryStrategies) {
      if (currentAttempt >= maxRetries) break;

      currentAttempt++;
      recoveryProgress = (currentAttempt / Math.min(maxRetries, recoveryStrategies.length)) * 100;
      
      recoveryMessage = `尝试策略 ${currentAttempt}: ${strategy.name}`;
      onRetry(currentAttempt);

      try {
        const success = await strategy.action();
        
        if (success) {
          recoveryMessage = '恢复成功！';
          recoveryProgress = 100;
          
          // 等待一下让用户看到成功消息
          await new Promise(resolve => setTimeout(resolve, 1000));
          
          isRecovering = false;
          onRecovered();
          return;
        }
      } catch (strategyError) {
        console.warn(`Recovery strategy "${strategy.name}" failed:`, strategyError);
      }

      // 策略失败，等待一下再尝试下一个
      if (currentAttempt < maxRetries && currentAttempt < recoveryStrategies.length) {
        recoveryMessage = `策略 ${currentAttempt} 失败，准备尝试下一个...`;
        await new Promise(resolve => setTimeout(resolve, retryDelay));
      }
    }

    // 所有策略都失败了
    recoveryMessage = '自动恢复失败';
    isRecovering = false;
    onFailed();
  }

  function stopRecovery() {
    if (recoveryTimer) {
      clearTimeout(recoveryTimer);
      recoveryTimer = null;
    }
    isRecovering = false;
    currentAttempt = 0;
    recoveryProgress = 0;
    recoveryMessage = '';
  }

  // 自动开始恢复
  $: if (autoRecovery && !isRecovering && recoveryStrategies.length > 0) {
    recoveryTimer = setTimeout(() => {
      startRecovery();
    }, 1000);
  }
</script>

<Card class="border-amber-200 bg-amber-50 dark:border-amber-800 dark:bg-amber-950/20">
  <CardHeader>
    <CardTitle class="flex items-center gap-2 text-amber-800 dark:text-amber-200">
      <Wrench class="h-5 w-5" />
      错误恢复助手
    </CardTitle>
    <CardDescription class="text-amber-700 dark:text-amber-300">
      {#if isRecovering}
        正在尝试自动恢复...
      {:else}
        可以尝试以下方法来解决问题
      {/if}
    </CardDescription>
  </CardHeader>

  <CardContent class="space-y-4">
    {#if isRecovering}
      <div class="space-y-3">
        <Progress value={recoveryProgress} class="h-2" />
        <div class="flex items-center justify-between text-sm">
          <span class="text-amber-700 dark:text-amber-300">
            {recoveryMessage}
          </span>
          <span class="text-amber-600 dark:text-amber-400">
            {currentAttempt}/{maxRetries}
          </span>
        </div>
        <Button variant="outline" size="sm" onclick={stopRecovery}>
          停止恢复
        </Button>
      </div>
    {:else}
      <div class="space-y-3">
        {#if recoveryStrategies.length > 0}
          <div class="space-y-2">
            <h4 class="text-sm font-medium text-amber-800 dark:text-amber-200">
              可用的恢复策略：
            </h4>
            <div class="space-y-2">
              {#each recoveryStrategies.slice(0, 3) as strategy}
                <div class="flex items-start gap-3 rounded-lg border border-amber-200 bg-white p-3 dark:border-amber-800 dark:bg-amber-950/10">
                  <span class="text-lg">{strategy.icon}</span>
                  <div class="flex-1 space-y-1">
                    <div class="font-medium text-amber-800 dark:text-amber-200">
                      {strategy.name}
                    </div>
                    <div class="text-sm text-amber-700 dark:text-amber-300">
                      {strategy.description}
                    </div>
                  </div>
                </div>
              {/each}
            </div>
          </div>
        {/if}

        <div class="flex gap-2">
          <Button onclick={startRecovery} disabled={recoveryStrategies.length === 0}>
            开始自动恢复
          </Button>
          <Button variant="outline" onclick={() => window.location.reload()}>
            手动刷新页面
          </Button>
        </div>
      </div>
    {/if}

    <!-- 系统状态显示 -->
    <div class="border-t border-amber-200 pt-4 dark:border-amber-800">
      <SystemStatus />
    </div>
  </CardContent>
</Card>
