/**
 * 错误报告和分析服务
 *
 * 提供错误收集、分析和报告功能
 *
 * @category Utils
 */

import { browser } from '$app/environment';
import type { ErrorContext, ErrorReport } from '$lib/components/features/error-handling/types';

import { createModuleLogger, type ErrorLogContext } from './logger';

const reportingLogger = createModuleLogger('error-reporting');

/**
 * 错误报告配置
 */
export interface ErrorReportingConfig {
  /** 是否启用错误报告 */
  enabled: boolean;
  /** 报告端点URL */
  endpoint?: string;
  /** 采样率 (0-1) */
  sampleRate: number;
  /** 批量发送大小 */
  batchSize: number;
  /** 发送间隔（毫秒） */
  flushInterval: number;
  /** 最大重试次数 */
  maxRetries: number;
  /** 是否包含用户信息 */
  includeUserInfo: boolean;
  /** 是否包含设备信息 */
  includeDeviceInfo: boolean;
}

/**
 * 错误报告服务类
 */
export class ErrorReportingService {
  private config: ErrorReportingConfig;
  private reportQueue: ErrorReport[] = [];
  private flushTimer: NodeJS.Timeout | null = null;
  private isInitialized = false;

  constructor(config: Partial<ErrorReportingConfig> = {}) {
    this.config = {
      enabled: false,
      sampleRate: 1.0,
      batchSize: 10,
      flushInterval: 30000, // 30秒
      maxRetries: 3,
      includeUserInfo: false,
      includeDeviceInfo: true,
      ...config,
    };
  }

  /**
   * 初始化错误报告服务
   */
  initialize(): void {
    if (this.isInitialized || !browser || !this.config.enabled) {
      return;
    }

    this.startFlushTimer();
    this.isInitialized = true;

    reportingLogger.info('ErrorReportingService initialized', {
      config: this.config,
    });
  }

  /**
   * 销毁错误报告服务
   */
  destroy(): void {
    if (!this.isInitialized) {
      return;
    }

    this.stopFlushTimer();
    this.flushReports(); // 发送剩余的报告
    this.reportQueue = [];
    this.isInitialized = false;

    reportingLogger.info('ErrorReportingService destroyed');
  }

  /**
   * 报告错误
   */
  reportError(error: Error, context: ErrorLogContext = {}, errorId?: string): void {
    if (!this.config.enabled || !this.shouldSample()) {
      return;
    }

    try {
      const report = this.createErrorReport(error, context, errorId);
      this.queueReport(report);
    } catch (reportError) {
      reportingLogger.error('Failed to create error report', {
        originalError: error.message,
        reportError: reportError instanceof Error ? reportError.message : String(reportError),
      });
    }
  }

  /**
   * 生成错误指纹
   */
  private generateFingerprint(error: Error, context: ErrorContext): string {
    const components = [
      error.name || 'Error',
      error.message.replace(/\d+/g, 'N'), // 简单的数字替换
      context.type,
      context.component || 'unknown',
    ];
    return this.hash(components.join('|'));
  }

  /**
   * 简单的哈希函数
   */
  private hash(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return Math.abs(hash).toString(16);
  }

  /**
   * 将ErrorLogContext转换为ErrorContext
   */
  private convertToErrorContext(logContext: ErrorLogContext): ErrorContext {
    return {
      type: (logContext.errorType as any) || 'unknown',
      severity: 'medium', // 默认严重程度
      timestamp: new Date().toISOString(),
      component: logContext.component,
      operation: logContext.operation,
      metadata: {
        ...logContext.context,
        url: logContext.url,
        userAgent: logContext.userAgent,
        requestId: logContext.requestId,
        statusCode: logContext.statusCode,
        attempts: logContext.attempts,
      }
    };
  }

  /**
   * 创建错误报告
   */
  private createErrorReport(error: Error, logContext: ErrorLogContext, errorId?: string): ErrorReport {
    const context = this.convertToErrorContext(logContext);
    const fingerprint = this.generateFingerprint(error, context);
    
    const report: ErrorReport = {
      errorId: errorId || `error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      fingerprint,
      message: error.message,
      stack: error.stack,
      context,
      timestamp: new Date().toISOString(),
      userAgent: browser ? navigator.userAgent : 'server',
      url: browser ? window.location.href : 'server',
    };

    // 添加用户和设备信息
    if (this.config.includeUserInfo) {
      report.userId = context.metadata?.userId as string;
      report.sessionId = context.metadata?.sessionId as string;
    }

    // 添加设备信息到report的context字段（使用any类型来绕过类型检查）
    if (this.config.includeDeviceInfo && browser) {
      (report as any).deviceInfo = this.getDeviceInfo();
    }

    return report;
  }

  /**
   * 获取设备信息
   */
  private getDeviceInfo(): Record<string, any> {
    if (!browser) {
      return {};
    }

    return {
      screen: {
        width: screen.width,
        height: screen.height,
        colorDepth: screen.colorDepth,
      },
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight,
      },
      language: navigator.language,
      platform: navigator.platform,
      cookieEnabled: navigator.cookieEnabled,
      onLine: navigator.onLine,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    };
  }

  /**
   * 判断是否应该采样
   */
  private shouldSample(): boolean {
    return Math.random() < this.config.sampleRate;
  }

  /**
   * 将报告加入队列
   */
  private queueReport(report: ErrorReport): void {
    this.reportQueue.push(report);

    reportingLogger.debug('Error report queued', {
      errorId: report.errorId,
      queueSize: this.reportQueue.length,
    });

    // 如果队列已满，立即发送
    if (this.reportQueue.length >= this.config.batchSize) {
      this.flushReports();
    }
  }

  /**
   * 发送报告
   */
  private async flushReports(): Promise<void> {
    if (this.reportQueue.length === 0 || !this.config.endpoint) {
      return;
    }

    const reportsToSend = [...this.reportQueue];
    this.reportQueue = [];

    try {
      await this.sendReports(reportsToSend);
      reportingLogger.info('Error reports sent successfully', {
        count: reportsToSend.length,
      });
    } catch (error) {
      reportingLogger.error('Failed to send error reports', {
        count: reportsToSend.length,
        error: error instanceof Error ? error.message : String(error),
      });

      // 重新加入队列进行重试（限制重试次数）
      const retriableReports = reportsToSend.filter((report) => {
        const retryCount = ((report.context.metadata as any)?.retryCount || 0) + 1;
        return retryCount <= this.config.maxRetries;
      });

      retriableReports.forEach((report) => {
        if (!report.context.metadata) {
          report.context.metadata = {};
        }
        (report.context.metadata as any).retryCount = ((report.context.metadata as any)?.retryCount || 0) + 1;
      });

      this.reportQueue.unshift(...retriableReports);
    }
  }

  /**
   * 发送报告到服务器
   */
  private async sendReports(reports: ErrorReport[]): Promise<void> {
    if (!this.config.endpoint) {
      throw new Error('No reporting endpoint configured');
    }

    const response = await fetch(this.config.endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        reports,
        metadata: {
          timestamp: new Date().toISOString(),
          userAgent: browser ? navigator.userAgent : 'server',
          url: browser ? window.location.href : 'server',
        },
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
  }

  /**
   * 启动定时发送
   */
  private startFlushTimer(): void {
    this.flushTimer = setInterval(() => {
      this.flushReports();
    }, this.config.flushInterval);
  }

  /**
   * 停止定时发送
   */
  private stopFlushTimer(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
      this.flushTimer = null;
    }
  }

  /**
   * 获取队列状态
   */
  getQueueStatus(): { size: number; config: ErrorReportingConfig } {
    return {
      size: this.reportQueue.length,
      config: { ...this.config },
    };
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<ErrorReportingConfig>): void {
    const wasEnabled = this.config.enabled;
    this.config = { ...this.config, ...newConfig };

    // 如果启用状态发生变化，重新初始化
    if (wasEnabled !== this.config.enabled) {
      if (this.config.enabled) {
        this.initialize();
      } else {
        this.destroy();
      }
    }

    reportingLogger.info('ErrorReportingService config updated', {
      config: this.config,
    });
  }
}

// 创建全局实例
export const errorReporting = new ErrorReportingService({
  enabled: import.meta.env.PROD, // 只在生产环境启用
  endpoint: import.meta.env.VITE_ERROR_REPORTING_ENDPOINT,
  sampleRate: parseFloat(import.meta.env.VITE_ERROR_SAMPLE_RATE || '1.0'),
});

// 在浏览器环境中自动初始化
if (browser) {
  errorReporting.initialize();
}
