<!-- src/routes/+layout.svelte -->
<script lang="ts">
  import '../app.css';

  import { ModeWatcher } from 'mode-watcher';
  import { onDestroy, onMount } from 'svelte';

  import { TabBar } from '$lib/components/layout';
  // 导入新的侧边栏组件
  import { AppSidebar, AppSidebarProvider } from '$lib/components/layout/AppSidebar';
  import { Separator } from '$lib/components/ui/separator/index.js';
  // shadcn-svelte 组件导入
  import * as Sidebar from '$lib/components/ui/sidebar/index.js';
  import { Toaster } from '$lib/components/ui/sonner';
  import { realTimeMessagesStore } from '$lib/stores/features/realTimeMessages';
  import { tabs } from '$lib/stores/ui';
  import { keyboardStore } from '$lib/stores/ui/keyboard';
  import { errorManager } from '$lib/utils/errorManager';
  import { performanceMonitor } from '$lib/utils/performanceMonitor';

  // 导入错误边界组件
  import { ErrorBoundary } from '../lib/components/features/error-handling';

  onMount(() => {
    // 性能监控：应用启动
    performanceMonitor.start('app-initialization');

    // 初始化错误管理器
    errorManager.initialize();

    // 初始化键盘快捷键
    keyboardStore.init();

    // 初始化实时消息服务（仅基础设置，不启动轮询）
    realTimeMessagesStore.initialize();

    // 性能监控：初始化完成
    performanceMonitor.end('app-initialization');

    // 在开发环境中启动 CPU 监控
    if (import.meta.env.DEV) {
      console.log('🚀 Application initialized, starting performance monitoring...');
      performanceMonitor.checkCPUUsage();
    }
  });

  onDestroy(() => {
    // 清理错误管理器
    errorManager.destroy();

    // 清理键盘快捷键监听
    keyboardStore.destroy();

    // 清理实时消息服务
    realTimeMessagesStore.destroy();
  });
</script>

<!-- ModeWatcher 组件 - 管理主题切换 -->
<ModeWatcher />

<!-- 使用 shadcn-svelte 侧边栏布局 -->
<AppSidebarProvider>
  <AppSidebar />
  <Sidebar.Inset>
    <!-- 头部区域：包含侧边栏触发器、TabBar和标题 -->
    <header
      class="border-sidebar-border flex h-12 shrink-0 items-center gap-1 border-b transition-[width,height] ease-linear"
    >
      <!-- 左侧：侧边栏触发器、TabBar和标题 -->
      <div class="flex min-w-0 flex-1 items-center gap-1 px-2">
        <Sidebar.Trigger class="-ml-1" />
        <Separator orientation="vertical" class="mx-1 data-[orientation=vertical]:h-4" />

        <!-- TabBar 标签页栏 - 紧挨着触发器 -->
        {#if $tabs.length > 0}
          <div class="ml-1 flex items-center">
            <TabBar />
          </div>
        {/if}
      </div>
    </header>

    <!-- 主内容区域 -->
    <main class="flex flex-1 flex-col">
      <!-- 页面内容区，可滚动 -->
      <div class="@container/main flex flex-1 flex-col p-4">
        <!-- 使用错误边界包装页面内容 -->
        <ErrorBoundary
          level="page"
          showDetails={import.meta.env.DEV}
          enableRecovery={true}
          onError={(error, context) => {
            // 可以在这里添加额外的错误处理逻辑
            console.error('Layout ErrorBoundary caught error:', error, context);
          }}
        >
          <!-- 使用 SvelteKit 的 slot 机制来显示页面内容 -->
          <slot />
        </ErrorBoundary>
      </div>
    </main>
  </Sidebar.Inset>
</AppSidebarProvider>

<!-- 全局通知系统 - 使用 Sonner Toast -->
<Toaster />

<!-- 移除了自定义样式，现在使用 shadcn-svelte 的标准样式 -->
