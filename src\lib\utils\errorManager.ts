/**
 * 全局错误管理器
 *
 * 统一处理应用中的所有错误，包括：
 * - JavaScript 运行时错误
 * - Promise 拒绝错误
 * - API 请求错误
 * - 组件错误
 *
 * @category Utils
 */

import { browser } from '$app/environment';
import type {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ErrorContext,
  ErrorManagerConfig,
  ErrorReport,
  ErrorStats,
  ErrorSeverity,
  ErrorType,
  ErrorFingerprint,
  ErrorSeverityCalculator,
} from '$lib/components/features/error-handling/types';
import { notifications } from '$lib/stores/ui/notifications';

import { errorReporting } from './errorReporting';
import {
  createModuleLogger,
  type ErrorLogContext,
  logApiError,
  logComponentError,
  logError,
} from './logger';

const errorLogger = createModuleLogger('error-manager');

/**
 * 错误指纹生成器实现
 */
class DefaultErrorFingerprint implements ErrorFingerprint {
  generate(error: Error, context: ErrorContext): string {
    const components = [
      error.name || 'Error',
      this.normalizeMessage(error.message),
      context.type,
      context.component || 'unknown',
      this.normalizeStack(error.stack)
    ];
    
    return this.hash(components.join('|'));
  }

  private normalizeMessage(message: string): string {
    // 移除动态部分（如时间戳、ID等）
    return message
      .replace(/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z/g, 'TIMESTAMP')
      .replace(/\b[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}\b/gi, 'UUID')
      .replace(/\b\d+\b/g, 'NUMBER');
  }

  private normalizeStack(stack?: string): string {
    if (!stack) return 'no-stack';
    
    // 只取前3行堆栈信息，忽略行号
    return stack
      .split('\n')
      .slice(0, 3)
      .map(line => line.replace(/:\d+:\d+/g, ':LINE:COL'))
      .join('|');
  }

  private hash(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash).toString(16);
  }
}

/**
 * 错误严重程度计算器实现
 */
class DefaultErrorSeverityCalculator implements ErrorSeverityCalculator {
  calculate(error: Error, context: ErrorContext): ErrorSeverity {
    // 路由错误通常是高严重程度
    if (context.type === 'route') {
      return 'high';
    }

    // 网络错误根据具体情况判断
    if (context.type === 'network') {
      if (error.message.includes('timeout') || error.message.includes('502')) {
        return 'medium';
      }
      return 'low';
    }

    // API错误根据状态码判断
    if (context.type === 'api') {
      const statusMatch = error.message.match(/(\d{3})/);
      if (statusMatch) {
        const status = parseInt(statusMatch[1]);
        if (status >= 500) return 'high';
        if (status >= 400) return 'medium';
      }
      return 'low';
    }

    // 组件错误根据错误类型判断
    if (context.type === 'component') {
      if (error.name === 'ChunkLoadError' || error.message.includes('chunk')) {
        return 'critical';
      }
      if (error.name === 'TypeError' && error.message.includes('Cannot read prop')) {
        return 'high';
      }
      return 'medium';
    }

    // 脚本错误
    if (context.type === 'script') {
      if (error.name === 'SyntaxError') return 'critical';
      if (error.name === 'ReferenceError') return 'high';
      return 'medium';
    }

    // 默认中等严重程度
    return 'medium';
  }
}

/**
 * 重构的错误管理器类
 */
export class ErrorManager {
  private config: ErrorManagerConfig;
  private errorCache: Map<string, ErrorStats> = new Map();
  private errorHandlers: Set<ErrorHandler> = new Set();
  private isInitialized = false;
  private fingerprintGenerator: ErrorFingerprint;
  private severityCalculator: ErrorSeverityCalculator;
  private deduplicationCache: Map<string, number> = new Map();

  constructor(config: Partial<ErrorManagerConfig> = {}) {
    this.config = {
      enableGlobalCapture: true,
      enableReporting: false,
      maxErrorCache: 100,
      showDetailsInDev: true,
      deduplicationWindow: 60000, // 1分钟
      sampleRate: 1.0,
      ...config,
    };

    this.fingerprintGenerator = new DefaultErrorFingerprint();
    this.severityCalculator = new DefaultErrorSeverityCalculator();
  }

  /**
   * 初始化错误管理器
   */
  initialize(): void {
    if (this.isInitialized || !browser) {
      return;
    }

    if (this.config.enableGlobalCapture) {
      this.setupGlobalErrorHandlers();
    }

    this.isInitialized = true;
    errorLogger.info('ErrorManager initialized', { config: this.config });
  }

  /**
   * 销毁错误管理器
   */
  destroy(): void {
    if (!this.isInitialized || !browser) {
      return;
    }

    this.removeGlobalErrorHandlers();
    this.errorCache.clear();
    this.errorHandlers.clear();
    this.isInitialized = false;

    errorLogger.info('ErrorManager destroyed');
  }

  /**
   * 设置全局错误处理器
   */
  private setupGlobalErrorHandlers(): void {
    // JavaScript 错误
    window.addEventListener('error', this.handleGlobalError);

    // Promise 拒绝错误
    window.addEventListener('unhandledrejection', this.handleUnhandledRejection);

    errorLogger.debug('Global error handlers attached');
  }

  /**
   * 移除全局错误处理器
   */
  private removeGlobalErrorHandlers(): void {
    window.removeEventListener('error', this.handleGlobalError);
    window.removeEventListener('unhandledrejection', this.handleUnhandledRejection);

    errorLogger.debug('Global error handlers removed');
  }

  /**
   * 处理全局 JavaScript 错误
   */
  private handleGlobalError = (event: ErrorEvent): void => {
    const error = new Error(event.message);
    error.stack = `${event.filename}:${event.lineno}:${event.colno}`;

    const context = this.createErrorContext({
      type: 'script',
      metadata: {
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
      },
    });

    this.captureError(error, context);
  };

  /**
   * 处理未捕获的 Promise 拒绝
   */
  private handleUnhandledRejection = (event: PromiseRejectionEvent): void => {
    const error = event.reason instanceof Error ? event.reason : new Error(String(event.reason));

    const context = this.createErrorContext({
      type: this.inferErrorType(error),
      metadata: {
        rejectionReason: event.reason,
        isPromiseRejection: true,
      },
    });

    this.captureError(error, context);
  };

  /**
   * 创建错误上下文
   */
  createErrorContext(partial: Partial<ErrorContext>): ErrorContext {
    const now = new Date().toISOString();
    const context: ErrorContext = {
      type: 'unknown',
      severity: 'medium',
      timestamp: now,
      ...partial,
    };

    // 如果没有指定严重程度，自动计算
    if (partial.severity === undefined && partial.type !== 'unknown') {
      // 需要错误对象来计算严重程度，但这里还没有，稍后在captureError中计算
    }

    return context;
  }

  /**
   * 推断错误类型
   */
  private inferErrorType(error: Error): ErrorType {
    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      return 'network';
    }
    
    if (error.name === 'ChunkLoadError' || error.message.includes('chunk')) {
      return 'component';
    }

    if (error.message.includes('404') || error.message.includes('route')) {
      return 'route';
    }

    if (error.message.includes('API') || error.message.includes('status')) {
      return 'api';
    }

    if (error.name === 'SyntaxError' || error.name === 'ReferenceError') {
      return 'script';
    }

    return 'unknown';
  }

  /**
   * 判断错误是否需要显示错误页面
   */
  shouldShowErrorPage(error: Error, context?: ErrorContext): boolean {
    if (!context) return false;

    // 严重程度为critical的错误需要显示错误页面
    if (context.severity === 'critical') {
      return true;
    }

    // 路由相关错误
    if (context.type === 'route') {
      return true;
    }

    // 组件致命错误（如chunk加载失败）
    if (context.type === 'component' && context.severity === 'high') {
      return true;
    }

    // 404 或页面不存在错误
    if (error.name === 'NotFound' ||
        error.message.includes('404') ||
        error.message.includes('Page not found')) {
      return true;
    }

    return false;
  }

  /**
   * 触发错误边界显示
   */
  triggerErrorBoundary(error: Error, context?: ErrorContext): void {
    if (browser) {
      // 广播给所有注册的 ErrorBoundary 组件
      window.dispatchEvent(new CustomEvent('error-boundary-trigger', {
        detail: { error, context }
      }));
    }
  }

  /**
   * 处理路由错误
   */
  handleRouteError(error: Error, route?: string): void {
    const context = this.createErrorContext({
      type: 'route',
      route,
      severity: 'high', // 路由错误通常是高严重程度
    });

    this.captureError(error, context);
  }

  /**
   * 捕获并处理错误（增强版）
   */
  captureError(error: Error, context?: ErrorContext): string {
    const errorId = this.generateErrorId();
    
    // 如果没有提供上下文，创建默认上下文
    if (!context) {
      context = this.createErrorContext({
        type: this.inferErrorType(error),
      });
    }

    // 自动计算严重程度（如果未指定）
    if (context.severity === 'medium' && context.type !== 'unknown') {
      context.severity = this.severityCalculator.calculate(error, context);
    }

    // 生成错误指纹
    const fingerprint = this.fingerprintGenerator.generate(error, context);

    // 检查去重
    if (this.isDuplicate(fingerprint)) {
      this.updateDuplicateError(fingerprint, errorId);
      return errorId; // 重复错误，不进行完整处理
    }

    // 检查是否应该抑制通知
    const suppressNotification = context.suppressNotification === true;

    // 创建错误统计信息
    const errorStats: ErrorStats = {
      errorId,
      fingerprint,
      timestamp: context.timestamp,
      context,
      message: error.message,
      userAgent: browser ? navigator.userAgent : 'server',
      url: browser ? window.location.href : 'server',
      resolved: false,
      count: 1,
    };

    // 缓存错误信息
    this.cacheError(errorStats);

    // 使用统一的错误日志记录
    const logContext: ErrorLogContext = {
      errorId,
      errorType: context.type,
      url: browser ? window.location.href : undefined,
      userAgent: browser ? navigator.userAgent : undefined,
      component: context.component,
      operation: context.operation,
      context: context.metadata,
    };

    logError(error, logContext, errorLogger);

    // 根据错误严重程度决定处理方式
    if (this.shouldShowErrorPage(error, context)) {
      // 触发错误边界
      this.triggerErrorBoundary(error, context);
    } else if (!suppressNotification) {
      // 显示通知
      this.showUserNotification(error, context);
    }

    // 调用注册的错误处理器
    this.notifyErrorHandlers(error, context);

    // 使用错误报告服务上报错误（采样）
    if (this.config.enableReporting && this.shouldSample()) {
      errorReporting.reportError(error, logContext, errorId);
    }

    return errorId;
  }

  /**
   * 检查是否为重复错误
   */
  private isDuplicate(fingerprint: string): boolean {
    const now = Date.now();
    const lastSeen = this.deduplicationCache.get(fingerprint);
    
    if (lastSeen && (now - lastSeen) < this.config.deduplicationWindow) {
      return true;
    }

    this.deduplicationCache.set(fingerprint, now);
    return false;
  }

  /**
   * 更新重复错误
   */
  private updateDuplicateError(fingerprint: string, errorId: string): void {
    // 查找现有错误并增加计数
    for (const [key, stats] of this.errorCache.entries()) {
      if (stats.fingerprint === fingerprint) {
        stats.count++;
        this.errorCache.set(key, stats);
        break;
      }
    }

    errorLogger.debug('Duplicate error detected', { fingerprint, errorId });
  }

  /**
   * 判断是否应该采样
   */
  private shouldSample(): boolean {
    return Math.random() < this.config.sampleRate;
  }

  /**
   * 生成错误ID
   */
  private generateErrorId(): string {
    return `error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 缓存错误信息
   */
  private cacheError(errorStats: ErrorStats): void {
    // 如果缓存已满，删除最旧的错误
    if (this.errorCache.size >= this.config.maxErrorCache) {
      const oldestKey = this.errorCache.keys().next().value;
      if (oldestKey) {
        this.errorCache.delete(oldestKey);
      }
    }

    this.errorCache.set(errorStats.errorId, errorStats);
  }

  /**
   * 显示用户通知
   */
  private showUserNotification(error: Error, context?: ErrorContext): void {
    const isDevMode = import.meta.env.DEV;
    const duration = context?.severity === 'critical' ? 0 : 5000; // critical错误不自动消失

    let title = '系统错误';
    let message = isDevMode ? error.message : '系统遇到了一个错误，我们已经记录了这个问题。';

    // 根据错误类型提供更具体的提示
    switch (context?.type) {
      case 'network':
        title = '网络错误';
        message = isDevMode ? error.message : '网络连接出现问题，请检查网络后重试。';
        break;
      case 'api':
        title = 'API错误';
        message = isDevMode ? error.message : '服务请求失败，请稍后重试。';
        break;
      case 'component':
        title = '组件错误';
        message = isDevMode ? error.message : '页面组件加载失败，请刷新页面。';
        break;
      case 'route':
        title = '页面错误';
        message = isDevMode ? error.message : '页面加载出现问题，请重试或返回首页。';
        break;
      case 'script':
        title = '脚本错误';
        message = isDevMode ? error.message : '页面脚本执行出错，请刷新页面。';
        break;
    }

    // 根据严重程度选择通知类型
    if (context?.severity === 'critical') {
      notifications.error(title, message, duration);
    } else if (context?.severity === 'high') {
      notifications.error(title, message, duration);
    } else if (context?.severity === 'medium') {
      notifications.warning(title, message, duration);
    } else {
      notifications.info(title, message, duration);
    }
  }

  /**
   * 通知注册的错误处理器
   */
  private notifyErrorHandlers(error: Error, context?: ErrorContext): void {
    this.errorHandlers.forEach((handler) => {
      try {
        handler(error, context);
      } catch (handlerError) {
        errorLogger.error('Error in error handler', {
          originalError: error.message,
          handlerError: handlerError instanceof Error ? handlerError.message : String(handlerError),
        });
      }
    });
  }

  /**
   * 注册错误处理器
   */
  addErrorHandler(handler: ErrorHandler): void {
    this.errorHandlers.add(handler);
  }

  /**
   * 移除错误处理器
   */
  removeErrorHandler(handler: ErrorHandler): void {
    this.errorHandlers.delete(handler);
  }

  /**
   * 获取错误统计信息
   */
  getErrorStats(): ErrorStats[] {
    return Array.from(this.errorCache.values());
  }

  /**
   * 标记错误为已解决
   */
  markErrorAsResolved(errorId: string): void {
    const errorStats = this.errorCache.get(errorId);
    if (errorStats) {
      errorStats.resolved = true;
      this.errorCache.set(errorId, errorStats);
      errorLogger.info('Error marked as resolved', { errorId });
    }
  }

  /**
   * 清除错误缓存
   */
  clearErrorCache(): void {
    this.errorCache.clear();
    errorLogger.info('Error cache cleared');
  }

  /**
   * 获取配置
   */
  getConfig(): ErrorManagerConfig {
    return { ...this.config };
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<ErrorManagerConfig>): void {
    this.config = { ...this.config, ...newConfig };
    errorLogger.info('ErrorManager config updated', { config: this.config });
  }

  /**
   * 创建组件特定的错误上下文
   */
  createComponentContext(componentName: string, operation?: string): ErrorContext {
    return this.createErrorContext({
      type: 'component',
      component: componentName,
      operation,
    });
  }

  /**
   * 创建API特定的错误上下文
   */
  createApiContext(endpoint: string, method: string = 'GET', statusCode?: number): ErrorContext {
    return this.createErrorContext({
      type: 'api',
      operation: `${method} ${endpoint}`,
      metadata: { endpoint, method, statusCode },
    });
  }

  /**
   * 创建网络特定的错误上下文
   */
  createNetworkContext(operation?: string): ErrorContext {
    return this.createErrorContext({
      type: 'network',
      operation,
    });
  }

  /**
   * 获取错误统计摘要
   */
  getErrorSummary(): {
    total: number;
    bySeverity: Record<ErrorSeverity, number>;
    byType: Record<ErrorType, number>;
    resolved: number;
    duplicates: number;
  } {
    const stats = this.getErrorStats();
    const summary = {
      total: stats.length,
      bySeverity: {
        low: 0,
        medium: 0,
        high: 0,
        critical: 0,
      } as Record<ErrorSeverity, number>,
      byType: {
        network: 0,
        component: 0,
        route: 0,
        api: 0,
        script: 0,
        unknown: 0,
      } as Record<ErrorType, number>,
      resolved: 0,
      duplicates: 0,
    };

    stats.forEach((stat) => {
      summary.bySeverity[stat.context.severity]++;
      summary.byType[stat.context.type]++;
      if (stat.resolved) summary.resolved++;
      if (stat.count > 1) summary.duplicates += stat.count - 1;
    });

    return summary;
  }

  /**
   * 清理过期的去重缓存
   */
  cleanupDeduplicationCache(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    for (const [key, timestamp] of this.deduplicationCache.entries()) {
      if (now - timestamp > this.config.deduplicationWindow) {
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach((key) => {
      this.deduplicationCache.delete(key);
    });

    if (expiredKeys.length > 0) {
      errorLogger.debug('Cleaned up expired deduplication entries', { 
        count: expiredKeys.length 
      });
    }
  }
}

// 创建全局实例
export const errorManager = new ErrorManager({
  enableGlobalCapture: true,
  enableReporting: import.meta.env.PROD, // 生产环境启用上报
  showDetailsInDev: import.meta.env.DEV,
  deduplicationWindow: 60000, // 1分钟去重窗口
  sampleRate: import.meta.env.PROD ? 0.1 : 1.0, // 生产环境10%采样，开发环境100%
  maxErrorCache: 50, // 降低缓存大小
});

// 在浏览器环境中自动初始化
if (browser) {
  errorManager.initialize();
  
  // 定期清理去重缓存
  setInterval(() => {
    errorManager.cleanupDeduplicationCache();
  }, 300000); // 每5分钟清理一次
}
