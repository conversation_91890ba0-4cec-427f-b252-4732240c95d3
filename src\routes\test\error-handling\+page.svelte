<script lang="ts">
  import { ErrorBoundary, ErrorDisplay, ErrorRecoveryHelper } from '$lib/components/features/error-handling';
  import { But<PERSON> } from '$lib/components/ui/button';
  import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '$lib/components/ui/card';
  import { Tabs, TabsContent, TabsList, TabsTrigger } from '$lib/components/ui/tabs';
  import { Alert, AlertDescription, AlertTitle } from '$lib/components/ui/alert';
  import { Separator } from '$lib/components/ui/separator';
  import { Badge } from '$lib/components/ui/badge';
  import { errorManager } from '$lib/utils/errorManager';
  
  // 状态变量
  let customErrorMessage = '自定义错误消息';
  let recoveryCount = 0;
  let errorSimulationActive = false;
  let errorStats = errorManager.getErrorSummary();
  
  // 定期更新错误统计
  setInterval(() => {
    errorStats = errorManager.getErrorSummary();
  }, 2000);
  
  // 模拟不同类型和严重程度的错误
  function simulateNetworkError() {
    const error = new Error('Failed to fetch: The request timed out');
    error.name = 'TypeError';
    const context = errorManager.createNetworkContext('获取用户数据');
    errorManager.captureError(error, context);
  }
  
  function simulateComponentError() {
    const error = new Error('Cannot read properties of undefined (reading \'name\')');
    error.name = 'TypeError';
    const context = errorManager.createComponentContext('UserProfile', '渲染用户信息');
    errorManager.captureError(error, context);
  }
  
  function simulateApiError() {
    const error = new Error('HTTP 500: Internal Server Error - Database connection failed');
    const context = errorManager.createApiContext('/api/users', 'GET', 500);
    errorManager.captureError(error, context);
  }
  
  function simulateCriticalError() {
    const error = new Error('Loading chunk 2 failed: ChunkLoadError');
    error.name = 'ChunkLoadError';
    const context = errorManager.createComponentContext('DashboardModule', '动态加载组件');
    errorManager.captureError(error, context);
  }
  
  function simulateRouteError() {
    const error = new Error('Route "/admin/secret-page" not found');
    error.name = 'NotFound';
    errorManager.handleRouteError(error, '/admin/secret-page');
  }
  
  function simulateScriptError() {
    const error = new Error('Unexpected token \'}\'');
    error.name = 'SyntaxError';
    const context = errorManager.createErrorContext({
      type: 'script',
      component: 'eval',
      operation: '执行动态脚本',
      metadata: {
        filename: 'dynamic-script.js',
        lineno: 15,
        colno: 8
      }
    });
    errorManager.captureError(error, context);
  }
  
  // 创建一个会抛出错误的组件
  let ErrorComponent: any;
  
  function createRenderError() {
    errorSimulationActive = true;
    // 通过errorManager触发错误，而不是直接在组件中抛错
    setTimeout(() => {
      const error = new Error('Component render failed: Missing required prop');
      const context = errorManager.createComponentContext('ErrorTestComponent', '渲染测试');
      context.severity = 'high'; // 设置为高严重程度，触发错误边界
      errorManager.captureError(error, context);
    }, 100);
  }
  
  function resetErrorComponent() {
    errorSimulationActive = false;
    ErrorComponent = null;
  }
  
  // 用于异步操作中触发错误
  async function triggerAsyncError() {
    try {
      await new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Promise rejected: Async operation failed')), 500);
      });
    } catch (error) {
      const context = errorManager.createNetworkContext('异步数据加载');
      errorManager.captureError(error as Error, context);
    }
  }
  
  // 触发未捕获的错误（用于测试全局错误处理）
  function triggerUncaughtError() {
    // 使用setTimeout让错误在下一个事件循环中抛出，避免被try/catch捕获
    setTimeout(() => {
      throw new Error('这是一个未捕获的错误，应该被全局错误处理器捕获');
    }, 0);
  }
  
  function triggerUncaughtPromiseRejection() {
    // 创建一个未处理的Promise拒绝
    new Promise((_, reject) => {
      setTimeout(() => reject(new Error('未处理的Promise拒绝')), 100);
    });
    // 注意：没有.catch()，所以会触发unhandledrejection事件
  }
  
  // 重置函数
  function resetErrors() {
    recoveryCount = 0;
    resetErrorComponent();
    errorManager.clearErrorCache();
    errorStats = errorManager.getErrorSummary();
  }
  
  // 恢复处理函数
  function handleRecovery() {
    recoveryCount++;
  }
  
  // 自定义错误触发函数
  function triggerCustomError() {
    const error = new Error(customErrorMessage);
    const context = errorManager.createComponentContext('CustomErrorTest', '自定义错误测试');
    context.severity = 'medium';
    errorManager.captureError(error, context);
  }
</script>

<div class="container mx-auto p-4 space-y-8">
  <div class="flex items-center justify-between">
    <h1 class="text-3xl font-bold">错误处理系统测试</h1>
    
    <!-- 错误统计面板 -->
    <div class="flex gap-2">
      <Badge variant="outline">
        总计: {errorStats.total}
      </Badge>
      <Badge variant="destructive">
        Critical: {errorStats.bySeverity.critical}
      </Badge>
      <Badge variant="secondary">
        High: {errorStats.bySeverity.high}
      </Badge>
      <Badge variant="default">
        Medium: {errorStats.bySeverity.medium}
      </Badge>
      <Badge variant="outline">
        Low: {errorStats.bySeverity.low}
      </Badge>
    </div>
  </div>
  
  <Tabs value="simulation">
    <TabsList>
      <TabsTrigger value="simulation">错误模拟</TabsTrigger>
      <TabsTrigger value="boundary">错误边界测试</TabsTrigger>
      <TabsTrigger value="recovery">错误恢复</TabsTrigger>
      <TabsTrigger value="stats">统计信息</TabsTrigger>
    </TabsList>
    
    <TabsContent value="simulation" class="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>错误类型模拟</CardTitle>
          <CardDescription>模拟不同类型和严重程度的错误，测试系统响应</CardDescription>
        </CardHeader>
        <CardContent class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="space-y-2">
            <h4 class="font-medium">网络和API错误</h4>
            <div class="space-y-2">
              <Button variant="destructive" size="sm" onclick={simulateNetworkError}>
                网络连接超时
              </Button>
              <Button variant="destructive" size="sm" onclick={simulateApiError}>
                API服务器错误 (500)
              </Button>
              <Button variant="destructive" size="sm" onclick={triggerAsyncError}>
                异步操作失败
              </Button>
            </div>
          </div>
          
          <div class="space-y-2">
            <h4 class="font-medium">组件和渲染错误</h4>
            <div class="space-y-2">
              <Button variant="destructive" size="sm" onclick={simulateComponentError}>
                组件属性错误
              </Button>
              <Button variant="destructive" size="sm" onclick={simulateCriticalError}>
                代码块加载失败 (Critical)
              </Button>
              <Button variant="destructive" size="sm" onclick={createRenderError}>
                渲染时错误 (触发边界)
              </Button>
            </div>
          </div>
          
          <div class="space-y-2">
            <h4 class="font-medium">路由和脚本错误</h4>
            <div class="space-y-2">
              <Button variant="destructive" size="sm" onclick={simulateRouteError}>
                路由未找到 (404)
              </Button>
              <Button variant="destructive" size="sm" onclick={simulateScriptError}>
                脚本语法错误
              </Button>
            </div>
          </div>
          
          <div class="space-y-2">
            <h4 class="font-medium">全局错误处理测试</h4>
            <div class="space-y-2">
              <Button variant="destructive" size="sm" onclick={triggerUncaughtError}>
                未捕获的错误
              </Button>
              <Button variant="destructive" size="sm" onclick={triggerUncaughtPromiseRejection}>
                未处理的Promise拒绝
              </Button>
            </div>
          </div>
        </CardContent>
        <CardFooter>
          <Button variant="outline" onclick={resetErrors}>
            清空错误缓存
          </Button>
        </CardFooter>
      </Card>
    </TabsContent>
    
    <TabsContent value="boundary" class="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>错误边界级别测试</CardTitle>
          <CardDescription>测试不同级别的ErrorBoundary组件</CardDescription>
        </CardHeader>
        <CardContent class="space-y-6">
          <!-- 页面级错误边界 -->
          <div class="space-y-2">
            <h4 class="font-medium">页面级错误边界 (Critical/High)</h4>
            <ErrorBoundary level="page" showDetails={true}>
              <div class="p-4 border border-red-200 rounded-lg">
                <p class="mb-2 text-sm text-gray-600">这个区域由页面级错误边界保护</p>
                <Button variant="destructive" onclick={() => {
                  const error = new Error('这是一个严重错误');
                  const context = errorManager.createComponentContext('PageLevelTest');
                  context.severity = 'critical';
                  errorManager.captureError(error, context);
                }}>
                  触发Critical错误
                </Button>
              </div>
            </ErrorBoundary>
          </div>
          
          <!-- 区域级错误边界 -->
          <div class="space-y-2">
            <h4 class="font-medium">区域级错误边界 (Medium/High 组件错误)</h4>
            <ErrorBoundary level="section" showDetails={false}>
              <div class="p-4 border border-yellow-200 rounded-lg">
                <p class="mb-2 text-sm text-gray-600">这个区域由区域级错误边界保护</p>
                <Button variant="destructive" onclick={() => {
                  const error = new Error('组件区域错误');
                  const context = errorManager.createComponentContext('SectionTest');
                  context.severity = 'medium';
                  errorManager.captureError(error, context);
                }}>
                  触发Medium组件错误
                </Button>
              </div>
            </ErrorBoundary>
          </div>
          
          <!-- 组件级错误边界 -->
          <div class="space-y-2">
            <h4 class="font-medium">组件级错误边界 (Low/Medium)</h4>
            <ErrorBoundary level="component" enableRecovery={true}>
              <div class="p-4 border border-blue-200 rounded-lg">
                <p class="mb-2 text-sm text-gray-600">这个区域由组件级错误边界保护</p>
                <Button variant="destructive" onclick={() => {
                  const error = new Error('轻微组件错误');
                  const context = errorManager.createComponentContext('ComponentTest');
                  context.severity = 'low';
                  errorManager.captureError(error, context);
                }}>
                  触发Low错误
                </Button>
              </div>
            </ErrorBoundary>
          </div>
          
          <!-- 自定义错误测试 -->
          <div class="space-y-2">
            <h4 class="font-medium">自定义错误消息测试</h4>
            <div class="flex gap-2">
              <input 
                type="text" 
                bind:value={customErrorMessage} 
                class="flex-1 p-2 border rounded-md"
                placeholder="输入自定义错误消息"
              />
              <Button variant="destructive" onclick={triggerCustomError}>
                触发自定义错误
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </TabsContent>
    
    <TabsContent value="recovery" class="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>错误恢复机制测试</CardTitle>
          <CardDescription>测试ErrorRecoveryHelper组件的自动恢复功能</CardDescription>
        </CardHeader>
        <CardContent>
          <ErrorBoundary level="component" enableRecovery={true} showDetails={true}>
            <div class="space-y-4">
              <Alert variant={recoveryCount > 0 ? "default" : "destructive"}>
                <AlertTitle>恢复状态监控</AlertTitle>
                <AlertDescription>
                  {#if recoveryCount > 0}
                    ✅ 已成功恢复 {recoveryCount} 次
                  {:else}
                    ⏳ 等待错误恢复操作
                  {/if}
                </AlertDescription>
              </Alert>
              
              <div class="flex gap-2">
                <Button onclick={() => handleRecovery()}>
                  模拟恢复成功
                </Button>
                
                <Button variant="destructive" onclick={() => {
                  const error = new Error("需要恢复的组件错误");
                  const context = errorManager.createComponentContext('RecoveryTest');
                  context.severity = 'medium';
                  errorManager.captureError(error, context);
                }}>
                  触发可恢复错误
                </Button>
              </div>
            </div>
          </ErrorBoundary>
        </CardContent>
      </Card>
    </TabsContent>
    
    <TabsContent value="stats" class="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>系统错误统计</CardTitle>
          <CardDescription>实时显示错误处理系统的运行状态</CardDescription>
        </CardHeader>
        <CardContent class="space-y-4">
          <!-- 总体统计 -->
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="text-center p-4 bg-gray-50 rounded-lg">
              <div class="text-2xl font-bold">{errorStats.total}</div>
              <div class="text-sm text-gray-600">总错误数</div>
            </div>
            <div class="text-center p-4 bg-green-50 rounded-lg">
              <div class="text-2xl font-bold text-green-600">{errorStats.resolved}</div>
              <div class="text-sm text-gray-600">已解决</div>
            </div>
            <div class="text-center p-4 bg-yellow-50 rounded-lg">
              <div class="text-2xl font-bold text-yellow-600">{errorStats.duplicates}</div>
              <div class="text-sm text-gray-600">重复错误</div>
            </div>
            <div class="text-center p-4 bg-blue-50 rounded-lg">
              <div class="text-2xl font-bold text-blue-600">{Object.keys(errorStats.byType).length}</div>
              <div class="text-sm text-gray-600">错误类型</div>
            </div>
          </div>
          
          <!-- 按严重程度分类 -->
          <div class="space-y-2">
            <h4 class="font-medium">按严重程度分类</h4>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
              {#each Object.entries(errorStats.bySeverity) as [severity, count]}
                <div class="flex justify-between p-2 bg-gray-50 rounded">
                  <span class="capitalize">{severity}</span>
                  <Badge variant={severity === 'critical' ? 'destructive' : severity === 'high' ? 'secondary' : 'outline'}>
                    {count}
                  </Badge>
                </div>
              {/each}
            </div>
          </div>
          
          <!-- 按类型分类 -->
          <div class="space-y-2">
            <h4 class="font-medium">按错误类型分类</h4>
            <div class="grid grid-cols-2 md:grid-cols-3 gap-2">
              {#each Object.entries(errorStats.byType) as [type, count]}
                <div class="flex justify-between p-2 bg-gray-50 rounded">
                  <span class="capitalize">{type}</span>
                  <Badge variant="outline">{count}</Badge>
                </div>
              {/each}
            </div>
          </div>
        </CardContent>
        <CardFooter>
          <Button variant="outline" onclick={() => {
            errorStats = errorManager.getErrorSummary();
          }}>
            刷新统计
          </Button>
        </CardFooter>
      </Card>
    </TabsContent>
  </Tabs>
  
  <Separator />
  
  <div>
    <h2 class="text-xl font-semibold mb-4">重构后的功能特性</h2>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <Card>
        <CardHeader>
          <CardTitle>智能错误分类</CardTitle>
        </CardHeader>
        <CardContent>
          <p class="text-sm">自动识别错误类型和严重程度，提供针对性的处理策略。</p>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>分层错误边界</CardTitle>
        </CardHeader>
        <CardContent>
          <p class="text-sm">页面、区域、组件三级错误边界，精确控制错误影响范围。</p>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>错误去重机制</CardTitle>
        </CardHeader>
        <CardContent>
          <p class="text-sm">基于指纹算法的错误去重，避免重复通知和处理。</p>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>智能恢复系统</CardTitle>
        </CardHeader>
        <CardContent>
          <p class="text-sm">根据错误类型自动选择恢复策略，提高系统稳定性。</p>
        </CardContent>
      </Card>
    </div>
  </div>
</div>